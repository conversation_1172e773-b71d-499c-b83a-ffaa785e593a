import os
from langchain_ollama import ChatOllama
from langchain.prompts import ChatPromptTemplate

# Configure Ollama endpoint and model
llm_base_url = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
model_name = os.getenv("LLM_CHOICE", "llama3.2")

chat = ChatOllama(base_url=llm_base_url, model=model_name)

# Use a chat-style prompt (system + human)
prompt = ChatPromptTemplate.from_messages([
    ("system", "You are an expert in answering questions about a pizza restaurant."),
    (
        "human",
        "Here are some relevant reviews: {reviews}\n\nHere is the question to answer: {question}"
    ),
])

# LCEL chain: prompt -> chat model
chain = prompt | chat

while True:
    print("\n\n----------------------------------------------------------")
    question = input("Ask a question (type 'q' to quit): )")
    print("\n\n")
    if question.lower() == "q":
        break

    result = chain.invoke({"reviews": [], "question": question})

    # Chat models return a message object; print only the content
    print(result)